"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipTrigger,
	TooltipProvider,
} from "@/components/ui/tooltip";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link, useNavigate } from "@tanstack/react-router";
import { BookOpen, ChevronRight, LogOut, User } from "react-feather";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import { useGetUser, useGetProfilePictureUrl } from "@/lib/queries/user.query";

export function UserNav() {
	const navigate = useNavigate();
	const { user, logout } = useAuthStore(
		useShallow((state) => ({
			user: state.user,
			logout: state.logout,
		}))
	);

	// Fetch user details from API using firebase ID
	const { data: userData } = useGetUser(user?.uid || "");
	const apiUser = userData?.data?.data;

	// Get profile picture URL
	const { data: profilePictureData } = useGetProfilePictureUrl(user?.uid || "");
	const profilePictureUrl = profilePictureData?.data?.data?.url;

	// Extract user details - prioritize database name if it exists and is not empty
	const displayName =
		apiUser?.name && apiUser.name.trim() !== ""
			? apiUser.name
			: user?.displayName || user?.email?.split("@")[0] || "User";

	const handleLogout = async () => {
		await logout();
		navigate({ to: "/account/login" });
	};

	// Get user initials for avatar fallback
	const getInitials = (name: string) => {
		if (!name) return "??";
		return name
			.split(" ")
			.map((part) => part.charAt(0).toUpperCase())
			.join("")
			.substring(0, 2);
	};

	return (
		<DropdownMenu>
			<TooltipProvider disableHoverableContent>
				<Tooltip delayDuration={100}>
					<TooltipTrigger asChild>
						<DropdownMenuTrigger asChild>
							<Button variant="ghost" className="px-0 hover:bg-white">
								<div className="flex items-center gap-x-3 w-full">
									<Avatar className="size-[42px]">
										<AvatarImage src={profilePictureUrl || ""} alt="Avatar" />
										<AvatarFallback>
											{getInitials(displayName)}
										</AvatarFallback>
									</Avatar>
									<div className="flex flex-col items-start grow justify-between ">
										<p className="text-base font-medium text-white lg:text-foreground">
											{displayName}
										</p>
										<p className="text-sm font-normal lg:hidden text-white">
											{user?.email}
										</p>
									</div>
									<ChevronRight
										size={20}
										className="text-white lg:text-foreground"
									/>
								</div>
							</Button>
						</DropdownMenuTrigger>
					</TooltipTrigger>
					<TooltipContent className="hidden lg:block" side="bottom">
						Profile
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>
			<DropdownMenuContent className="w-56" align="end" forceMount>
				<DropdownMenuLabel className="font-normal">
					<div className="flex flex-col space-y-1">
						<p className="text-sm font-medium leading-none">
							{displayName}
						</p>
						<p className="text-xs leading-none text-slate-500 dark:text-slate-400">
							{user?.email}
						</p>
					</div>
				</DropdownMenuLabel>
				<DropdownMenuSeparator />
				<DropdownMenuGroup>
					<DropdownMenuItem className="hover:cursor-pointer" asChild>
						<Link to="/dashboard" className="flex items-center">
							<BookOpen className="w-4 h-4 mr-3 text-slate-500 dark:text-slate-400" />
							Dashboard
						</Link>
					</DropdownMenuItem>
					<DropdownMenuItem className="hover:cursor-pointer" asChild>
						<Link to="/account" className="flex items-center">
							<User className="w-4 h-4 mr-3 text-slate-500 dark:text-slate-400" />
							Account
						</Link>
					</DropdownMenuItem>
				</DropdownMenuGroup>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="hover:cursor-pointer text-red-500 focus:text-red-500"
					onClick={handleLogout}
				>
					<LogOut className="w-4 h-4 mr-3 text-red-500" />
					Logout
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
