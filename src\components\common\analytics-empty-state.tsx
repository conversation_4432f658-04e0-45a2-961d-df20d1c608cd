import React from "react";
import { useNavigate } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { BookOpen, TrendingUp } from "react-feather";

export interface AnalyticsEmptyStateProps {
	variant: "no-data" | "no-tests-completed";
	className?: string;
}

export const AnalyticsEmptyState: React.FC<AnalyticsEmptyStateProps> = ({
	variant,
	className = "",
}) => {
	const navigate = useNavigate();

	const handleNavigateToMockTest = () => {
		navigate({ to: "/t/mock" });
	};

	if (variant === "no-data") {
		return (
			<div
				className={`mb-9 px-6 py-12 bg-white rounded-3xl border border-gray-300 flex flex-col items-center justify-center text-center ${className}`}
			>
				<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
					<TrendingUp className="w-8 h-8 text-gray-400" />
				</div>
				<h3 className="text-lg font-semibold text-gray-700 mb-2">
					No Analytics Available
				</h3>
				<p className="text-sm text-gray-500 max-w-md">
					Analytics data is currently unavailable. Please try again later.
				</p>
			</div>
		);
	}

	return (
		<div
			className={`mb-9 px-6 py-12 bg-white rounded-3xl border border-gray-300 flex flex-col items-center justify-center text-center ${className}`}
		>
			<div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
				<BookOpen className="w-8 h-8 text-purple-600" />
			</div>
			<h3 className="text-lg font-semibold text-gray-700 mb-2">
				Complete Mock Tests to Get Analytics
			</h3>
			<p className="text-sm text-gray-500 mb-6 max-w-md">
				Start taking mock tests to see your performance analytics, progress
				tracking, and personalized insights.
			</p>
			<Button
				onClick={handleNavigateToMockTest}
				className="bg-[#5936CD] hover:bg-[#4A2BA8] text-white px-6 py-2 rounded-lg font-medium transition-colors"
			>
				Take Mock Test
			</Button>
		</div>
	);
};
