"use client";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
	Too<PERSON><PERSON>,
	TooltipTrigger,
	TooltipContent,
	TooltipProvider,
} from "@/components/ui/tooltip";
import { Bell, Globe, HelpCircle, LogOut, Settings } from "react-feather";
import { Link } from "@tanstack/react-router";
import { SheetClose } from "@/components/ui/sheet";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";

interface MenuProps {
	isOpen: boolean | undefined;
}

const menus = [
	{ href: "", label: "Notifications", icon: Bell, submenus: [] },
	{ href: "", label: "Language", icon: Globe, submenus: [] },
	{ href: "/settings", label: "Settings", icon: Settings, submenus: [] },
	{ href: "/help", label: "Help", icon: HelpCircle, submenus: [] },
	{ href: "", label: "Logout", icon: LogOut, submenus: [] },
];

export function MobileMenu({ isOpen }: MenuProps) {
	const { logout } = useAuthStore(
		useShallow((state) => ({
			logout: state.logout,
		}))
	);
	const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
		const value = e.currentTarget.value;
		switch (value) {
			case "Logout":
				logout();
				break;
			default:
				break;
		}
	};
	return (
		<ul className="flex border-y border-y-[#E2E8F01A] grow flex-col items-start space-y-1 px-2 py-6">
			{menus.map(({ href, label, icon: Icon }, index) => (
				<li className="w-full" key={index}>
					<TooltipProvider disableHoverableContent>
						<Tooltip delayDuration={100}>
							<TooltipTrigger asChild>
								<SheetClose asChild>
									<Button
										className={cn(
											"rounded-full text-base w-full justify-start gap-3"
										)}
										asChild={href.length > 0}
										value={label}
										onClick={handleClick}
									>
										{href.length > 0 ? (
											<Link to={href}>
												<span className={cn(isOpen === false ? "" : "mr-0")}>
													<Icon size={24} />
												</span>
												<p
													className={cn(
														"max-w-[200px] truncate",
														isOpen === false
															? "-translate-x-96 opacity-0"
															: "translate-x-0 opacity-100"
													)}
												>
													{label}
												</p>
											</Link>
										) : (
											<>
												<span className={cn(isOpen === false ? "" : "mr-0")}>
													<Icon size={24} />
												</span>
												<p
													className={cn(
														"max-w-[200px] truncate",
														isOpen === false
															? "-translate-x-96 opacity-0"
															: "translate-x-0 opacity-100"
													)}
												>
													{label}
												</p>
											</>
										)}
									</Button>
								</SheetClose>
							</TooltipTrigger>
							{isOpen === false && (
								<TooltipContent side="right">{label}</TooltipContent>
							)}
						</Tooltip>
					</TooltipProvider>
				</li>
			))}
		</ul>
	);
}
