import { create<PERSON>ile<PERSON><PERSON><PERSON>, useNavigate, <PERSON> } from "@tanstack/react-router";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
	Edit,
	Book,
	Database,
	Key,
	FileText,
	FileMinus,
	Clock,
} from "react-feather";
import { useGetUser, useGetProfilePictureUrl } from "@/lib/queries/user.query";
import { AnalyticsCard, LoadingCard } from "@/components/common";

// Custom simple loading state component
const LoadingBlock = ({ className = "" }: { className?: string }) => (
	<div className={`animate-pulse bg-slate-100 rounded-md ${className}`}></div>
);

const AccountPage = () => {
	const navigate = useNavigate();

	// Get Firebase user details
	const { user } = useAuthStore(
		useShallow((state) => ({
			user: state.user,
		}))
	);

	// Fetch user details from API using firebase ID
	const { data: userData, isLoading } = useGetUser(user?.uid || "");
	const apiUser = userData?.data?.data;

	// Get profile picture URL
	const { data: profilePictureData } = useGetProfilePictureUrl(user?.uid || "");
	const profilePictureUrl = profilePictureData?.data?.data?.url;

	// Extract user details - prioritize database name if it exists and is not empty
	const displayName =
		apiUser?.name && apiUser.name.trim() !== ""
			? apiUser.name
			: user?.displayName || user?.email?.split("@")[0] || "User";

	// Get user initials for avatar fallback
	const getInitials = (name: string) => {
		if (!name) return "??";
		return name
			.split(" ")
			.map((part) => part.charAt(0).toUpperCase())
			.join("")
			.substring(0, 2);
	};

	return (
		<div className="container mx-auto p-4 pb-20 lg:pb-8">
			{/* Profile Section */}
			<div className="flex flex-col items-start w-full">
				{/* Cover Background */}
				<div className="w-full h-[178px] bg-[#5936CD] rounded-t-lg relative overflow-hidden">
					{/* Background Pattern/Circles (simplified) */}
					<div className="absolute w-[515px] h-[329.26px] left-[-67px] top-[-190px]">
						<div className="absolute w-[294.18px] h-[294.18px] right-[713px] top-[-190px] bg-gradient-to-b from-[rgba(239,242,255,0.4)] to-[rgba(239,242,255,0)]"></div>
						<div className="absolute w-[294.18px] h-[294.18px] left-[-67px] top-[-190px] bg-gradient-to-b from-[rgba(239,242,255,0.4)] to-[rgba(239,242,255,0)]"></div>
						<div className="absolute w-[293.71px] h-[294.18px] left-[calc(50%-293.71px/2-389.27px)] top-[-154.92px] bg-gradient-to-b from-[rgba(239,242,255,0.4)] to-[rgba(239,242,255,0)]"></div>
					</div>
				</div>

				{/* User Info Section */}
				<div className="flex flex-row items-center w-full pl-6 pr-6 -mt-[75px]">
					{/* Avatar */}
					<div className="w-[150px] h-[150px] relative">
						<Avatar className="w-[150px] h-[150px] border-4 border-[#F8FAFC] bg-[#D9D9D9]">
							<AvatarImage src={profilePictureUrl} alt="User avatar" />
							<AvatarFallback className="text-[48px] text-[#64748B]">
								{getInitials(displayName)}
							</AvatarFallback>
						</Avatar>
					</div>

					{/* Name + Edit Button */}
					<div className="flex flex-col justify-end items-start pl-8 gap-[11.48px]">
						<div className="flex flex-row items-center gap-[6.26px]">
							<h1 className="font-bold text-2xl leading-[29px] text-[#211C37]">
								{displayName}
							</h1>
						</div>
						<div className="flex flex-row items-center gap-2">
							<Button
								variant="link"
								className="p-0 font-medium text-[16px] leading-[140%] text-[#5936CD] underline"
								onClick={() => {
									navigate({ to: "/edit-account" as any });
								}}
							>
								Edit Profile
							</Button>
							<Button
								variant="link"
								className="p-0 hover:bg-transparent"
								onClick={() => {
									navigate({ to: "/edit-account" as any });
								}}
							>
								<Edit size={20} className="text-[#5936CD]" />
							</Button>
						</div>
					</div>
				</div>

				{/* Analytics Cards */}
				<div className="w-full mt-10">
					<div
						className="p-6 bg-white rounded-2xl mb-6"
						style={{ boxShadow: "6px 6px 54px 0px #0000000D" }}
					>
						<div className="flex justify-between items-center mb-6">
							<h2 className="font-bold text-2xl text-[#202224]">
								Education Details
							</h2>
						</div>

						{isLoading ? (
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
								{[...Array(4)].map((_, i) => (
									<div
										key={i}
										className="border border-[#CBD5E1] rounded-lg p-4 shadow-sm"
									>
										<div className="flex flex-col gap-4">
											<LoadingBlock className="w-10 h-10 rounded-full" />
											<div>
												<LoadingBlock className="h-4 w-24 mb-2" />
												<LoadingBlock className="h-6 w-16" />
											</div>
										</div>
									</div>
								))}
							</div>
						) : (
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
								{/* Card 1 */}
								<div className="border border-[#CBD5E1] rounded-lg p-4 shadow-sm">
									<div className="flex flex-col gap-4">
										<div className="flex items-center justify-center w-10 h-10 rounded-full bg-[rgba(89,54,205,0.1)]">
											<Book size={20} className="text-[#5936CD]" />
										</div>
										<div>
											<p className="text-[#64748B] text-sm font-medium opacity-70">
												Education Background
											</p>
											<p className="text-[#334155] text-xl font-semibold">
												{apiUser?.educationBackground || "N/A"}
											</p>
										</div>
									</div>
								</div>

								{/* Card 2 */}
								<div className="border border-[#CBD5E1] rounded-lg p-4 shadow-sm">
									<div className="flex flex-col gap-4">
										<div className="flex items-center justify-center w-10 h-10 rounded-full bg-[rgba(89,54,205,0.1)]">
											<Key size={20} className="text-[#5936CD]" />
										</div>
										<div>
											<p className="text-[#64748B] text-sm font-medium opacity-70">
												Subject Group
											</p>
											<p className="text-[#334155] text-xl font-semibold">
												{Array.isArray(apiUser?.subjectGroup)
													? apiUser.subjectGroup.join(", ")
													: apiUser?.subjectGroup || "N/A"}
											</p>
										</div>
									</div>
								</div>

								{/* Card 3 */}
								<div className="border border-[#CBD5E1] rounded-lg p-4 shadow-sm">
									<div className="flex flex-col gap-4">
										<div className="flex items-center justify-center w-10 h-10 rounded-full bg-[rgba(89,54,205,0.1)]">
											<Database size={20} className="text-[#5936CD]" />
										</div>
										<div>
											<p className="text-[#64748B] text-sm font-medium opacity-70">
												Class
											</p>
											<p className="text-[#334155] text-xl font-semibold">
												{apiUser?.currentClass || "N/A"}
											</p>
										</div>
									</div>
								</div>

								{/* Card 4 */}
								<div className="border border-[#CBD5E1] rounded-lg p-4 shadow-sm">
									<div className="flex flex-col gap-4">
										<div className="flex items-center justify-center w-10 h-10 rounded-full bg-[rgba(89,54,205,0.1)]">
											<Database size={20} className="text-[#5936CD]" />
										</div>
										<div>
											<p className="text-[#64748B] text-sm font-medium opacity-70">
												Target Tests
											</p>
											<p className="text-[#334155] text-xl font-semibold">
												{Array.isArray(apiUser?.targetEntryTests)
													? apiUser.targetEntryTests.join(", ")
													: apiUser?.targetEntryTests || "N/A"}
											</p>
										</div>
									</div>
								</div>
							</div>
						)}
					</div>

					{/* Activity Analytics */}
					<div
						className="p-6 bg-white rounded-2xl"
						style={{ boxShadow: "6px 6px 54px 0px #0000000D" }}
					>
						<div className="flex justify-between items-center mb-6">
							<h2 className="font-bold text-2xl text-[#202224]">Activity</h2>
							<Button variant="outline" className="rounded-lg" asChild>
								<Link to="/analytics">View Details</Link>
							</Button>
						</div>

						{isLoading ? (
							<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
								{[...Array(3)].map((_, i) => (
									<LoadingCard key={i} variant="analytics" />
								))}
							</div>
						) : (
							<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
								<AnalyticsCard
									heading="MCQs Solved"
									number={apiUser?.analytics?.mcqsSolvedCount || 0}
									icon={FileText}
									helperText="Down from yesterday"
									colorVariant="blue"
									trendDirection="down"
									trendPercentage="-"
								/>
								<AnalyticsCard
									heading="Avg Score Per Quiz"
									number={apiUser?.analytics?.avgScorePerQuiz || 0}
									icon={FileMinus}
									helperText="Up from past week"
									colorVariant="red"
									trendDirection="up"
									trendPercentage="-"
								/>
								<AnalyticsCard
									heading="Avg Time Per Quiz"
									number={apiUser?.analytics?.avgTimePerQuiz || 0}
									icon={Clock}
									helperText="Up from yesterday"
									colorVariant="green"
									trendDirection="up"
									trendPercentage="-"
								/>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/account")({
	component: AccountPage,
});

export default AccountPage;
